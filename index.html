<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>办公文件批示辅助工具</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="index.css">
<link rel="stylesheet" href="/index.css">
</head>
<body>
    <div id="app">
        <el-container id="main-container" style="min-height: 100vh; display: flex; flex-direction: column;">
            <el-header class="app-header">
                <h1>办公文件批示辅助工具</h1>
            </el-header>
            <el-main style="flex-grow: 1;">
                <el-row :gutter="20" class="card-container" type="flex" style="flex-wrap: wrap;">
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="group in endorsementGroups" :key="group.id" class="card-col" style="display: flex;">
                        <el-card class="endorsement-card" :style="{ backgroundColor: group.color }" style="width: 100%; display: flex; flex-direction: column;">
                            <div slot="header" class="clearfix">
                                <span>{{ group.name }}</span>
                            </div>
                            <div style="flex-grow: 1;">
                                <div
                                    v-for="(instruction, index) in group.instructions"
                                    :key="index"
                                    class="instruction-item"
                                    @click="copyInstruction(instruction, $event)"
                                    @keyup.enter="copyInstruction(instruction, $event)"
                                    @keyup.space="copyInstruction(instruction, $event)"
                                    role="button"
                                    tabindex="0"
                                    :aria-label="'复制批示: ' + instruction"
                                >
                                    {{ instruction }}
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-main>
            <el-footer class="app-footer">
                © 2024 办公文件批示辅助工具
            </el-footer>
        </el-container>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.11/dist/clipboard.min.js"></script>
    <script type="module" src="index.js"></script>
<script type="module" src="/index.tsx"></script>
</body>
</html>
