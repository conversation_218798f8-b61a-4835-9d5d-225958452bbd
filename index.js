// Vue and ClipboardJS are loaded globally via <script> tags in index.html

document.addEventListener('DOMContentLoaded', () => {
    if (typeof Vue === 'undefined') {
        console.error('Vue is not loaded. Please check the script tag in index.html.');
        alert('错误：Vue.js未能加载，应用无法启动。');
        return;
    }
    if (typeof ClipboardJS === 'undefined') {
        console.error('ClipboardJS is not loaded. Please check the script tag in index.html.');
        // Vue instance might not be available yet if this check is too early.
        // Alert can be used as a fallback if $message is not yet available.
    }

    new Vue({
        el: '#app',
        data: {
            endorsementGroups: [
                {
                    id: 'leader1',
                    name: '班子',
                    color: '#e6f7ff', // Light Blue
                    instructions: [
                        '请永波同志阅\r\n请丽群、沈俊、冯飞同志阅示。',
                        '请永波同志阅\r\n请丽群、沈俊、冯飞同志阅示\r\n发各科室。'
                    ]
                },
                {
                    id: 'leader2',
                    name: '李局',
                    color: '#fff7e6', // Light Orange
                    instructions: [
                        '请X同志参阅。',
                        '请X同志核示。',
                        '按此执行。',
                        '已悉。',
                        '请抓紧落实。'
                    ]
                },
                {
                    id: 'dept1',
                    name: '办公室',
                    color: '#f6ffed', // Light Green
                    instructions: [
                        '请办公室办理。',
                        '请办公室存档。',
                        '请办公室分发。',
                        '转办公室处理。',
                        '请办公室催办。'
                    ]
                },
                {
                    id: 'dept2',
                    name: '财务科',
                    color: '#fff1f0', // Light Red/Pink
                    instructions: [
                        '请财务科核报。',
                        '请财务科审核。',
                        '转财务科办理。',
                        '请财务科阅。'
                    ]
                },
                {
                    id: 'dept3',
                    name: '综合事务部',
                    color: '#fcf0ff', // Light Purple
                    instructions: [
                        '请综合部协调。',
                        '请综合部汇总上报。',
                        '转综合部阅存。'
                    ]
                },
                 {
                    id: 'general',
                    name: '通用批示',
                    color: '#e9e9eb', // Light Grey
                    instructions: [
                        '拟同意。',
                        '请酌处。',
                        '建议按计划执行。',
                        '存档。'
                    ]
                }
            ]
        },
        methods: {
            copyInstruction(instructionText, event) {
                if (typeof ClipboardJS === 'undefined') {
                     this.$message.error({
                        message: '复制功能库未加载，无法复制。',
                        showClose: true,
                        duration: 3000
                    });
                    // Attempt fallback for modern browsers even if ClipboardJS main lib failed
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(instructionText).then(() => {
                            this.$message.success({ message: '已复制 (备用): ' + instructionText, showClose: true, duration: 1500 });
                            const clickedElement = event.currentTarget;
                            clickedElement.classList.add('highlight');
                            setTimeout(() => { clickedElement.classList.remove('highlight'); }, 350);
                        }).catch(err => {
                            this.$message.error({ message: '备用复制方法也失败了。请手动复制。', showClose: true, duration: 2000 });
                            console.error('Fallback clipboard error:', err);
                        });
                    } else {
                        alert('无法复制文本，请手动复制。');
                    }
                    return;
                }

                const clickedElement = event.currentTarget;

                const fakeElement = document.createElement('button');
                fakeElement.style.position = 'absolute';
                fakeElement.style.left = '-9999px';
                fakeElement.style.opacity = '0';
                fakeElement.setAttribute('aria-hidden', 'true');
                fakeElement.setAttribute('tabindex', '-1');
                
                document.body.appendChild(fakeElement);

                const clipboardInstance = new ClipboardJS(fakeElement, {
                    text: () => instructionText,
                    action: () => 'copy',
                    container: document.body 
                });

                clipboardInstance.on('success', (e) => {
                    this.$message.success({
                        message: '已复制: ' + e.text,
                        showClose: true,
                        duration: 1500
                    });
                    e.clearSelection();
                    
                    clickedElement.classList.add('highlight');
                    setTimeout(() => {
                        clickedElement.classList.remove('highlight');
                    }, 350); 

                    clipboardInstance.destroy();
                    document.body.removeChild(fakeElement);
                });

                clipboardInstance.on('error', (e) => {
                    this.$message.error({
                        message: '复制失败，请尝试手动复制。',
                        showClose: true,
                        duration: 2000
                    });
                    console.error('ClipboardJS Error:', e);
                    
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(instructionText).then(() => {
                            this.$message.success({ message: '已复制 (备用): ' + instructionText, showClose: true, duration: 1500 });
                            clickedElement.classList.add('highlight');
                            setTimeout(() => { clickedElement.classList.remove('highlight'); }, 350);
                        }).catch(err => {
                            this.$message.error({ message: '备用复制方法也失败了。请手动复制。', showClose: true, duration: 2000 });
                            console.error('Fallback clipboard error:', err);
                        });
                    } else {
                         alert('复制失败，您的浏览器不支持备用复制方法，请手动复制。');
                    }
                    
                    clipboardInstance.destroy();
                    document.body.removeChild(fakeElement);
                });

                fakeElement.click();
            }
        }
    });
});
